import axios from 'axios';

// Get API URL from environment variable or use default
const API_URL = window.location.hostname === 'localhost' 
    ? 'http://localhost:8000/api'
    : '/api';  // Use relative path in production

// Create axios instance with default config
const api = axios.create({
    baseURL: API_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Announcement API functions
export const announcementApi = {
    // Get all announcements with optional filters
    getAnnouncements: async (params = {}) => {
        try {
            const response = await api.get('/announcements/', { params });
            return response.data;
        } catch (error) {
            console.error('Error fetching announcements:', error);
            throw error;
        }
    },

    // Get single announcement by ID
    getAnnouncement: async (id) => {
        try {
            const response = await api.get(`/announcements/${id}/`);
            return response.data;
        } catch (error) {
            console.error('Error fetching announcement:', error);
            throw error;
        }
    },

    // Create new announcement
    createAnnouncement: async (data) => {
        try {
            const response = await api.post('/announcements/', data);
            return response.data;
        } catch (error) {
            console.error('Error creating announcement:', error);
            throw error;
        }
    },

    // Update existing announcement
    updateAnnouncement: async (id, data) => {
        try {
            const response = await api.patch(`/announcements/${id}/`, data);
            return response.data;
        } catch (error) {
            console.error('Error updating announcement:', error);
            throw error;
        }
    },

    // Delete announcement
    deleteAnnouncement: async (id) => {
        try {
            const response = await api.delete(`/announcements/${id}/`);
            return response.data;
        } catch (error) {
            console.error('Error deleting announcement:', error);
            throw error;
        }
    },

    // Toggle pin status
    togglePin: async (id) => {
        try {
            const response = await api.post(`/announcements/${id}/toggle_pin/`);
            return response.data;
        } catch (error) {
            console.error('Error toggling pin status:', error);
            throw error;
        }
    },

    // Increment views
    incrementViews: async (id) => {
        try {
            const response = await api.post(`/announcements/${id}/increment_views/`);
            return response.data;
        } catch (error) {
            console.error('Error incrementing views:', error);
            throw error;
        }
    },

    // Force expire announcement
    forceExpire: async (id) => {
        try {
            const response = await api.post(`/announcements/${id}/force_expire/`);
            return response.data;
        } catch (error) {
            console.error('Error forcing expiration:', error);
            throw error;
        }
    },

    // Restore expired announcement
    restore: async (id) => {
        try {
            const response = await api.post(`/announcements/${id}/restore/`);
            return response.data;
        } catch (error) {
            console.error('Error restoring announcement:', error);
            throw error;
        }
    },

    // Update all announcement statuses
    updateStatuses: async () => {
        try {
            const response = await api.post('/announcements/update_statuses/');
            return response.data;
        } catch (error) {
            console.error('Error updating statuses:', error);
            throw error;
        }
    }
};

// Tower API functions
export const towerApi = {
  // Get all towers
  getTowers: async () => {
    try {
      const response = await api.get('/towers/');
      return response.data;
    } catch (error) {
      console.error('Error fetching towers:', error);
      throw error;
    }
  }
};

// Unit API functions
export const unitApi = {
  // Get all units or units filtered by tower IDs
  getUnits: async (towerIds = null) => {
    try {
      const params = towerIds ? { tower_ids: towerIds.join(',') } : {};
      const response = await api.get('/units/', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching units:', error);
      throw error;
    }
  }
};

// Attachment API functions
export const attachmentApi = {
  // Upload attachment
  uploadAttachment: async (announcementId, file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('announcement', announcementId);

      const response = await api.post('/attachments/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading attachment:', error);
      throw error;
    }
  },

  // Delete attachment
  deleteAttachment: async (id) => {
    try {
      const response = await api.delete(`/attachments/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Error deleting attachment:', error);
      throw error;
    }
  }
};

// Utility function to get current user from localStorage
export const getCurrentUser = () => {
  try {
    const member = localStorage.getItem('member');
    return member ? JSON.parse(member) : null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// Utility function to format announcement data for API
export const formatAnnouncementForApi = (formData, attachments = []) => {
  const currentUser = getCurrentUser();

  // Validate required fields first
  const requiredFields = ['title', 'startDate', 'startTime', 'endDate', 'endTime', 'label'];
  const missingFields = requiredFields.filter(field => !formData[field] || formData[field].trim() === '');
  
  if (missingFields.length > 0) {
    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
  }

  // Ensure priority has a valid value - must match backend PRIORITY_CHOICES
  let priority = 'normal'; // default
  if (formData.priority && formData.priority.trim() !== '') {
    const priorityValue = formData.priority.toLowerCase();
    // Validate against backend choices: 'low', 'normal', 'high', 'urgent'
    if (['low', 'normal', 'high', 'urgent'].includes(priorityValue)) {
      priority = priorityValue;
    } else {
      throw new Error('Invalid priority value. Must be one of: low, normal, high, urgent');
    }
  }

  // Ensure label has a valid value
  let label = formData.label || '';
  if (!label || label.trim() === '') {
    throw new Error('Label is required');
  }

  // Ensure post_as has a valid value - must match backend POST_AS_CHOICES
  let post_as = 'creator'; // default
  if (formData.postAs && formData.postAs.trim() !== '') {
    const postAsValue = formData.postAs.toLowerCase();
    // Validate against backend choices: 'creator', 'group', 'member'
    if (['creator', 'group', 'member'].includes(postAsValue)) {
      post_as = postAsValue;
    } else {
      throw new Error('Invalid post_as value. Must be one of: creator, group, member');
    }
  }

  // Validate post_as specific requirements
  if (post_as === 'group' && !formData.selectedGroupId) {
    throw new Error('Group ID is required when posting as a group');
  }
  if (post_as === 'member' && !formData.selectedMemberId) {
    throw new Error('Member ID is required when posting as a member');
  }

  // Ensure target_tower_ids and target_unit_ids are arrays of integers
  let target_tower_ids = [];
  if (formData.selectedTowers && Array.isArray(formData.selectedTowers)) {
    target_tower_ids = formData.selectedTowers
      .map(id => parseInt(id, 10))
      .filter(id => !isNaN(id) && id > 0);
  }

  let target_unit_ids = [];
  if (formData.selectedUnits && Array.isArray(formData.selectedUnits)) {
    target_unit_ids = formData.selectedUnits
      .map(id => parseInt(id, 10))
      .filter(id => !isNaN(id) && id > 0);
  }

  // Validate dates and times
  const startDateTime = new Date(`${formData.startDate}T${formData.startTime}`);
  const endDateTime = new Date(`${formData.endDate}T${formData.endTime}`);
  
  if (isNaN(startDateTime.getTime())) {
    throw new Error('Invalid start date or time format');
  }
  if (isNaN(endDateTime.getTime())) {
    throw new Error('Invalid end date or time format');
  }
  if (endDateTime <= startDateTime) {
    throw new Error('End date/time must be after start date/time');
  }

  // Process attachments for base64 format
  const base64_attachments = [];
  if (attachments && Array.isArray(attachments)) {
    for (const attachment of attachments) {
      if (attachment.base64) {
        // Validate base64 data
        if (!attachment.base64.startsWith('data:') && !attachment.base64.startsWith('http')) {
          throw new Error('Invalid attachment format');
        }
        
        base64_attachments.push({
          base64: attachment.base64,
          name: attachment.name || attachment.file?.name || 'attachment',
          type: attachment.type || attachment.file?.type || 'application/octet-stream'
        });
      }
    }
  }

  // Prepare the base announcement data
  const announcementData = {
    title: formData.title.trim(),
    description: formData.description ? formData.description.trim() : '',  // Make description optional
    post_as: post_as,
    priority: priority,
    label: label.trim(),
    start_date: formData.startDate,
    start_time: formData.startTime,
    end_date: formData.endDate,
    end_time: formData.endTime,
    target_tower_ids: target_tower_ids,
    target_unit_ids: target_unit_ids,
    base64_attachments: base64_attachments
  };

  // Add posted_group or posted_member based on post_as value
  if (post_as === 'group' && formData.selectedGroupId) {
    announcementData.posted_group = parseInt(formData.selectedGroupId, 10);
  } else if (post_as === 'member' && formData.selectedMemberId) {
    announcementData.posted_member = parseInt(formData.selectedMemberId, 10);
  }

  return announcementData;
};
