import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ArrowLeft, Upload, X } from 'lucide-react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import AnnouncementPreview from './components/AnnouncementPreview';
import PriorityDropdown from './components/PriorityDropdown';
import LabelSelector from './components/LabelSelector';
import Calendar from './components/Calendar';
import TimePicker from './components/TimePicker';
import TowerSelector from './components/TowerSelector';
import UnitSelector from './components/UnitSelector';
import GroupSelector from './components/GroupSelector';
import MemberSelector from './components/MemberSelector';
import MessageBox from '../../../Components/MessageBox/MessageBox';
import ErrorMessage from '../../../Components/MessageBox/ErrorMessage';
import { announcementApi } from "../../../api/announcementsApi/announcementsBackendApi";

// Validation schema
const announcementSchema = yup.object().shape({
  title: yup
    .string()
    .required('Title is required')
    .test('word-count', 'Title must be 10 words or less', (value) => {
      if (!value) return true;
      return value.trim().split(/\s+/).length <= 10;
    }),
  description: yup
    .string()
    .test('word-count', 'Description must be 100 words or less', (value) => {
      if (!value) return true;
      return value.trim().split(/\s+/).length <= 100;
    }),
  postAs: yup.string().required('Post as selection is required'),
  creatorName: yup.string().required('Creator name is required'),
  selectedMemberId: yup.string().when('postAs', {
    is: 'Member',
    then: (schema) => schema.required('Please select a member'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedMemberName: yup.string().when('postAs', {
    is: 'Member',
    then: (schema) => schema.required('Please select a member'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedGroupId: yup.string().when('postAs', {
    is: 'Group',
    then: (schema) => schema.required('Please select a group'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedGroupName: yup.string().when('postAs', {
    is: 'Group',
    then: (schema) => schema.required('Please select a group'),
    otherwise: (schema) => schema.notRequired()
  }),
  priority: yup.string().required('Priority is required'),
  label: yup.string().required('Label is required'),
  startDate: yup.string().required('Start date is required'),
  startTime: yup.string().required('Start time is required'),
  endDate: yup.string().required('End date is required'),
  endTime: yup.string().required('End time is required'),
  selectedTowers: yup.array(), // Made optional to match backend
  selectedUnits: yup.array(), // Made optional to match backend
  attachments: yup.array()
});

/**
 * EditAnnouncement Component
 * Form for editing existing announcements with real-time preview
 */
const EditAnnouncement = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams();
  const [currentUser, setCurrentUser] = useState(null);
  const [attachments, setAttachments] = useState([]);
  const [originalAttachments, setOriginalAttachments] = useState([]);
  const [attachmentsToDelete, setAttachmentsToDelete] = useState([]);
  const [announcement, setAnnouncement] = useState(null);
  const [loading, setLoading] = useState(true);
  const [successMessage, setSuccessMessage] = useState('');
  const [fileUploadError, setFileUploadError] = useState('');
  const [error, setError] = useState(null);

  // Get the source tab from location state (passed from AnnouncementList)
  const sourceTab = location.state?.sourceTab || null;

  // Form setup with validation
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    reset,
    formState: { errors, isSubmitting, isValid }
  } = useForm({
    resolver: yupResolver(announcementSchema),
    mode: 'onChange',
    defaultValues: {
      title: '',
      description: '',
      postAs: 'Creator',
      creatorName: '',
      autoName: '',
      selectedMemberId: '',
      selectedMemberName: '',
      selectedGroupId: '',
      selectedGroupName: '',
      priority: 'normal',
      label: '',
      startDate: '',
      startTime: '',
      endDate: '',
      endTime: '',
      selectedTowers: [],
      selectedUnits: [],
      attachments: []
    }
  });

  // Check if all required fields are filled
  const isFormValid = () => {
    const values = getValues();
    return (
      values.title &&
      values.creatorName &&
      values.priority &&
      values.label &&
      values.startDate &&
      values.startTime &&
      values.endDate &&
      values.endTime &&
      values.selectedTowers?.length > 0 &&
      values.selectedUnits?.length > 0 &&
      (values.postAs === 'Creator' ||
        (values.postAs === 'Group' && values.selectedGroupId) ||
        (values.postAs === 'Member' && values.selectedMemberId))
    );
  };

  // Watch all form values for real-time preview
  const watchedValues = watch();

  // Watch creator name for auto-sync
  const creatorName = watch('creatorName');

  // Watch selected towers for unit filtering
  const selectedTowers = watch('selectedTowers');

  // State to track if announcement data has been loaded
  const [announcementLoaded, setAnnouncementLoaded] = useState(false);

  // Force unit selector to refresh when towers are loaded and announcement data is available
  useEffect(() => {
    if (selectedTowers && selectedTowers.length > 0 && announcementLoaded && announcement) {
      // Small delay to ensure units are fetched before setting values
      const timer = setTimeout(() => {
        const originalUnits = announcement.target_units_data?.map(unit => unit.id) || [];
        if (originalUnits.length > 0) {
          console.log('Restoring original units:', originalUnits);
          setValue('selectedUnits', originalUnits);
        }
      }, 500); // 500ms delay to allow units to load

      return () => clearTimeout(timer);
    }
  }, [selectedTowers, setValue, announcementLoaded, announcement]);

  // Update autoName when creatorName changes
  useEffect(() => {
    setValue('autoName', creatorName);
  }, [creatorName, setValue]);

  // Helper function to convert backend post_as value to frontend format
  const convertPostAsToFrontend = (backendValue) => {
    const mapping = {
      'creator': 'Creator',
      'group': 'Group',
      'member': 'Member'
    };
    return mapping[backendValue] || 'Creator';
  };

  // Helper function to convert frontend postAs value to backend format
  const convertPostAsToBackend = (frontendValue) => {
    const mapping = {
      'Creator': 'creator',
      'Group': 'group',
      'Member': 'member'
    };
    return mapping[frontendValue] || 'creator';
  };

  // Get current user info on component mount
  useEffect(() => {
    // Get user data from localStorage (set during login)
    const getCurrentUser = () => {
      try {
        const member = localStorage.getItem('member');
        return member ? JSON.parse(member) : null;
      } catch (error) {
        console.error('Error getting current user:', error);
        return null;
      }
    };

    const user = getCurrentUser();
    if (user) {
      setCurrentUser(user);
      // Set default creator name
      setValue('creatorName', user.full_name || user.fullName || 'Current User');
    }

    // Load saved post type preference
    const savedPostAs = localStorage.getItem('announcementPostAs');
    if (savedPostAs) {
      setValue('postAs', savedPostAs);
    }
  }, [setValue]);

  // Handle member selection
  const handleMemberSelect = (memberData) => {
    if (memberData) {
      setValue('selectedMemberId', memberData.id);
      setValue('selectedMemberName', memberData.name);
      // Set creator name to selected member when member is selected
      setValue('creatorName', memberData.name);
    } else {
      setValue('selectedMemberId', '');
      setValue('selectedMemberName', '');
      // Reset creator name to current user when member selection is cleared (like Group behavior)
      const user = currentUser || (() => {
        try {
          const member = localStorage.getItem('member');
          return member ? JSON.parse(member) : null;
        } catch (error) {
          return null;
        }
      })();
      if (user) {
        setValue('creatorName', user.full_name || user.fullName || 'Current User');
      }
    }
  };

  // Handle group selection
  const handleGroupSelect = (groupData) => {
    if (groupData) {
      setValue('selectedGroupId', groupData.id);
      setValue('selectedGroupName', groupData.name);
      // Set creator name to current user when group is selected
      if (currentUser) {
        setValue('creatorName', currentUser.full_name || currentUser.fullName || 'Current User');
      }
    } else {
      setValue('selectedGroupId', '');
      setValue('selectedGroupName', '');
    }
  };

  // Load announcement data
  useEffect(() => {
    const loadAnnouncement = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch announcement data from API
        console.log('Fetching announcement with ID:', id);
        const response = await announcementApi.getAnnouncement(id);
        console.log('API Response:', response);

        // Fix: The API returns data directly, not nested in response.data
        const announcementData = response;
        console.log('Announcement Data:', announcementData);

        if (announcementData) {
          setAnnouncement(announcementData);

          // Populate form with existing data
          reset({
            title: announcementData.title || '',
            description: announcementData.description || '',
            postAs: convertPostAsToFrontend(announcementData.post_as),
            creatorName: announcementData.creator_name || '',
            autoName: announcementData.creator_name || '',
            selectedMemberId: announcementData.posted_member || '',
            selectedMemberName: announcementData.member_name || '',
            selectedGroupId: announcementData.posted_group || '',
            selectedGroupName: announcementData.group_name || '',
            priority: announcementData.priority || '',
            label: announcementData.label || '',
            startDate: announcementData.start_date || '',
            startTime: announcementData.start_time || '',
            endDate: announcementData.end_date || '',
            endTime: announcementData.end_time || '',
            selectedTowers: announcementData.target_towers_data?.map(tower => tower.id) || [],
            selectedUnits: announcementData.target_units_data?.map(unit => unit.id) || [],
            attachments: announcementData.attachments || []
          });

          // Set attachments if they exist
          if (announcementData.attachments) {
            const existingAttachments = announcementData.attachments.map((att, index) => ({
              id: att.id || index,
              url: att.file_url,
              name: att.file_name,
              type: att.file_type,
              isExisting: true // Mark as existing attachment
            }));
            setAttachments(existingAttachments);
            setOriginalAttachments(existingAttachments);
          }

          // Mark announcement as loaded
          setAnnouncementLoaded(true);
        } else {
          setError('Announcement not found');
          navigate('/announcements');
        }
      } catch (error) {
        console.error('Error loading announcement:', error);
        setError(error.response?.data?.message || 'Error loading announcement');
        navigate('/announcements');
      } finally {
        setLoading(false);
      }
    };

    loadAnnouncement();
  }, [id, reset, navigate]);

  // Utility function to convert file to base64
  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);

    // Check if adding these files would exceed the 5-file limit
    if (attachments.length + files.length > 5) {
      setFileUploadError('Please upload maximum 5 files to proceed.');
      // Reset the file input
      event.target.value = '';
      return;
    }

    // Check file types and sizes
    const validFiles = [];
    const invalidFiles = [];

    for (const file of files) {
      const isImage = file.type.startsWith('image/');
      const isPDF = file.type === 'application/pdf';
      const isDoc = file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

      if (!isImage && !isPDF && !isDoc) {
        invalidFiles.push(file.name);
        continue;
      }

      // Check file size: 5MB for all files
      if (file.size > 5 * 1024 * 1024) {
        setFileUploadError(`File "${file.name}" exceeds 5MB limit.`);
        // Reset the file input
        event.target.value = '';
        return;
      }

      validFiles.push(file);
    }

    if (invalidFiles.length > 0) {
      setFileUploadError(`Invalid file types: ${invalidFiles.join(', ')}. Only images, PDF, and DOC files are allowed.`);
      // Reset the file input
      event.target.value = '';
      return;
    }

    // Clear any previous error
    setFileUploadError('');

    try {
      const newAttachments = await Promise.all(
        validFiles.map(async (file) => {
          const base64 = await fileToBase64(file);
          return {
            id: Date.now() + Math.random(),
            file,
            url: base64, // Use base64 instead of blob URL
            base64: base64, // Store base64 for saving
            name: file.name,
            type: file.type
          };
        })
      );

      setAttachments(prev => [...prev, ...newAttachments]);
    } catch (error) {
      console.error('Error processing files:', error);
    }

    // Reset the file input to allow selecting the same file again
    event.target.value = '';
  };

  // Remove attachment
  const removeAttachment = (id) => {
    const attachmentToRemove = attachments.find(att => att.id === id);

    if (attachmentToRemove && attachmentToRemove.isExisting) {
      // If it's an existing attachment, add to deletion list
      setAttachmentsToDelete(prev => [...prev, id]);
    }

    // Remove from current attachments list
    setAttachments(prev => prev.filter(att => att.id !== id));
  };

  // Utility function to determine announcement status based on dates
  const getAnnouncementStatus = (startDate, startTime, endDate, endTime) => {
    if (!startDate || !startTime || !endDate || !endTime) {
      return 'draft';
    }

    try {
      const now = new Date();

      // Handle different date formats and ensure proper parsing
      let startDateStr = startDate;
      let endDateStr = endDate;

      // If dates are in YYYY-MM-DD format, use them directly
      if (typeof startDate === 'string' && startDate.includes('-')) {
        startDateStr = startDate;
      } else {
        // Convert to YYYY-MM-DD format if needed
        const startDateObj = new Date(startDate);
        startDateStr = startDateObj.toISOString().split('T')[0];
      }

      if (typeof endDate === 'string' && endDate.includes('-')) {
        endDateStr = endDate;
      } else {
        // Convert to YYYY-MM-DD format if needed
        const endDateObj = new Date(endDate);
        endDateStr = endDateObj.toISOString().split('T')[0];
      }

      // Create datetime objects for comparison
      const startDateTime = new Date(`${startDateStr}T${startTime}`);
      const endDateTime = new Date(`${endDateStr}T${endTime}`);

      if (now < startDateTime) {
        return 'upcoming';
      } else if (now >= startDateTime && now <= endDateTime) {
        return 'ongoing';
      } else {
        return 'expired';
      }
    } catch (error) {
      console.warn('Error calculating announcement status:', error);
      return 'draft';
    }
  };

  // Word count utility functions
  const countWords = (text) => {
    if (!text || text.trim() === '') return 0;
    return text.trim().split(/\s+/).length;
  };

  // Handle title input change to limit words
  const handleTitleChange = (value, onChange) => {
    if (!value || value.trim() === '') {
      onChange('');
      return;
    }

    const words = value.trim().split(/\s+/);
    const limited = words.slice(0, 10).join(' ');
    onChange(limited);
  };

  const handleDescriptionKeyDown = (e, currentValue) => {
    const currentWordCount = countWords(currentValue);

    // If we're under the limit, allow all typing
    if (currentWordCount < 100) {
      return; // Allow the keystroke
    }

    // If we're at or over 100 words, block EVERYTHING except backspace and delete
    if (currentWordCount >= 100) {
      // Only allow backspace and delete to remove content
      if (e.key === 'Backspace' || e.key === 'Delete') {
        return; // Allow deletion
      }

      // Block everything else - no typing, no spaces, no navigation
      e.preventDefault();
      return;
    }
  };

  // Handle form submission
  const onSubmit = async (data) => {
    try {
      setError(null);
      console.log('Updated Announcement Data:', data);

      // Prepare new attachments (base64 format)
      const newAttachments = attachments
        .filter(att => !att.isExisting && att.base64)
        .map(att => ({
          base64: att.base64,
          name: att.name,
          type: att.type
        }));

      // Prepare announcement data with correct field names for backend
      const announcementData = {
        title: data.title,
        description: data.description,
        post_as: convertPostAsToBackend(data.postAs),
        priority: data.priority,
        label: data.label,
        start_date: data.startDate,
        start_time: data.startTime,
        end_date: data.endDate,
        end_time: data.endTime,
        target_tower_ids: data.selectedTowers,
        target_unit_ids: data.selectedUnits,
        base64_attachments: newAttachments,
        attachments_to_delete: attachmentsToDelete,
        posted_group: data.selectedGroupId,
        posted_member: data.selectedMemberId,
        group_name: data.selectedGroupName,
        member_name: data.selectedMemberName
      };

      console.log('Sending update data:', announcementData);
      console.log('New attachments:', newAttachments);
      console.log('Attachments to delete:', attachmentsToDelete);

      // Update announcement via API
      const response = await announcementApi.updateAnnouncement(id, announcementData);
      console.log('Update response:', response);

      if (response) {
        console.log('Announcement updated successfully');
        setSuccessMessage('Announcement has been successfully updated.');
      }
    } catch (error) {
      console.error('Error updating announcement:', error);
      console.error('Error details:', error.response?.data);
      setError(error.response?.data?.message || error.message || 'Error updating announcement');
    }
  };

  // Handle back navigation
  const handleBack = () => {
    // Navigate back to the same tab the user came from
    const targetTab = sourceTab || 1; // Default to ongoing tab if no source tab
    navigate('/announcements', {
      state: {
        activeTab: targetTab,
        announcementId: id // Pass the announcement ID to trigger refresh
      },
      replace: true
    });
  };

  // Clear success message
  const clearMessage = () => {
    setSuccessMessage('');
  };

  // Handle success message OK button
  const handleSuccessOk = () => {
    // Navigate back to announcements list with the correct tab
    const targetTab = sourceTab || 1; // Default to ongoing tab if no source tab
    navigate('/announcements', {
      state: {
        activeTab: targetTab,
        announcementId: id // Pass the announcement ID to trigger refresh
      },
      replace: true
    });
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-[#3D9D9B] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-500">Loading announcement...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">{error}</div>
          <button
            onClick={() => navigate('/announcements', {
              state: {
                activeTab: sourceTab || 1,
                announcementId: id
              }
            })}
            className="px-4 py-2 bg-[#3D9D9B] text-white rounded-lg hover:bg-[#2d7a78] transition-colors"
          >
            Back to Announcements
          </button>
        </div>
      </div>
    );
  }

  // Calculate current status based on dates and times
  const getCurrentStatus = () => {
    if (!watchedValues.startDate || !watchedValues.startTime || !watchedValues.endDate || !watchedValues.endTime) {
      return 'draft';
    }

    // Use the existing getAnnouncementStatus function
    return getAnnouncementStatus(
      watchedValues.startDate,
      watchedValues.startTime,
      watchedValues.endDate,
      watchedValues.endTime
    );
  };

  // Get status styling
  const getStatusStyling = (status) => {
    switch (status) {
      case 'upcoming':
        return {
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
          borderColor: 'border-blue-200',
          icon: '📅'
        };
      case 'ongoing':
        return {
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          borderColor: 'border-green-200',
          icon: '🟢'
        };
      case 'expired':
        return {
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          borderColor: 'border-red-200',
          icon: '🔴'
        };
      default:
        return {
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-200',
          icon: '📝'
        };
    }
  };

  // Prepare data for preview component
  const previewData = {
    title: watchedValues.title,
    description: watchedValues.description,
    postAs: watchedValues.postAs,
    authorName: watchedValues.creatorName,
    selectedGroupName: watchedValues.selectedGroupName,
    selectedMemberName: watchedValues.selectedMemberName,
    priority: watchedValues.priority,
    label: watchedValues.label,
    startDate: watchedValues.startDate,
    startTime: watchedValues.startTime,
    endDate: watchedValues.endDate,
    endTime: watchedValues.endTime,
    attachments: attachments.map(att => ({
      preview: att.base64 || att.url, // Use base64 for preview
      name: att.name,
      type: att.type
    }))
  };

  return (
    <div className="min-h-screen bg-[#F5F5F5]">
      {/* Header */}
      <div className=" shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={handleBack}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              <span className="text-lg font-semibold">Edit Announcement</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left Column - Preview */}
          <div className="order-2 lg:order-1 lg:col-span-4">
            <div className="bg-white rounded-lg shadow-sm p-6 lg:sticky lg:top-8 h-screen lg:h-[calc(100vh-6rem)] overflow-y-auto">
              {/* <h3 className="text-lg font-semibold text-[#3D9D9B] mb-4">Preview</h3> */}
              <AnnouncementPreview data={previewData} currentUser={currentUser} />
            </div>
          </div>

          {/* Right Column - Form (wider) */}
          <div className="order-1 lg:order-2 lg:col-span-8">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Announcement Author Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-[#3D9D9B] mb-4">Announcement Author</h3>

                {/* Creator Name and Post as on different rows */}
                <div className="space-y-4">
                  {/* Creator Name */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Creator Name <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="creatorName"
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          type="text"
                          readOnly
                          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-[#999999] cursor-not-allowed"
                          value={currentUser?.full_name || currentUser?.fullName || 'Current User'}
                        />
                      )}
                    />
                  </div>

                  {/* Post as */}
                  <div>
                    <div className="flex items-center mb-3">
                      <label className="block text-sm font-semibold text-gray-700">
                        Post as <span className="text-[#3D9D9B]">*</span>
                      </label>
                      <div className="ml-8">
                        <Controller
                          name="postAs"
                          control={control}
                          render={({ field }) => (
                            <div className="flex space-x-6">
                              <label className={`flex items-center ${announcement?.post_as === 'creator' ? 'cursor-pointer group' : 'cursor-not-allowed opacity-50'}`}>
                                <div className="relative">
                                  <input
                                    type="radio"
                                    {...field}
                                    value="Creator"
                                    checked={field.value === 'Creator'}
                                    onChange={(e) => {
                                      if (announcement?.post_as === 'creator') {
                                        field.onChange(e.target.value);
                                        // Clear member and group selections when switching to Creator
                                        setValue('selectedMemberId', '');
                                        setValue('selectedMemberName', '');
                                        setValue('selectedGroupId', '');
                                        setValue('selectedGroupName', '');
                                        // Set creator name to current user
                                        const user = currentUser || (() => {
                                          try {
                                            const member = localStorage.getItem('member');
                                            return member ? JSON.parse(member) : null;
                                          } catch (error) {
                                            return null;
                                          }
                                        })();
                                        if (user) {
                                          setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                        }
                                      }
                                    }}
                                    disabled={announcement?.post_as !== 'creator'}
                                    className="sr-only peer"
                                  />
                                  <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-[#3D9D9B] peer-checked:border-4"></div>
                                </div>
                                <span className="ml-2 text-sm text-gray-700">Creator</span>
                              </label>
                              <label className={`flex items-center ${announcement?.post_as === 'group' ? 'cursor-pointer group' : 'cursor-not-allowed opacity-50'}`}>
                                <div className="relative">
                                  <input
                                    type="radio"
                                    {...field}
                                    value="Group"
                                    checked={field.value === 'Group'}
                                    onChange={(e) => {
                                      if (announcement?.post_as === 'group') {
                                        field.onChange(e.target.value);
                                        // Clear member selection when switching to Group
                                        setValue('selectedMemberId', '');
                                        setValue('selectedMemberName', '');
                                        // Clear group selection to allow fresh selection
                                        setValue('selectedGroupId', '');
                                        setValue('selectedGroupName', '');
                                        // Set creator name to current user when switching to Group
                                        const user = currentUser || (() => {
                                          try {
                                            const member = localStorage.getItem('member');
                                            return member ? JSON.parse(member) : null;
                                          } catch (error) {
                                            return null;
                                          }
                                        })();
                                        if (user) {
                                          setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                        }
                                      }
                                    }}
                                    disabled={announcement?.post_as !== 'group'}
                                    className="sr-only peer"
                                  />
                                  <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-[#3D9D9B] peer-checked:border-4"></div>
                                </div>
                                <span className="ml-2 text-sm text-gray-700">Group</span>
                              </label>
                              <label className={`flex items-center ${announcement?.post_as === 'member' ? 'cursor-pointer group' : 'cursor-not-allowed opacity-50'}`}>
                                <div className="relative">
                                  <input
                                    type="radio"
                                    {...field}
                                    value="Member"
                                    checked={field.value === 'Member'}
                                    onChange={(e) => {
                                      if (announcement?.post_as === 'member') {
                                        field.onChange(e.target.value);
                                        // Clear member and group selections when switching to Member
                                        setValue('selectedMemberId', '');
                                        setValue('selectedMemberName', '');
                                        setValue('selectedGroupId', '');
                                        setValue('selectedGroupName', '');
                                        // Set creator name to current user when switching to Member (like Group)
                                        const user = currentUser || (() => {
                                          try {
                                            const member = localStorage.getItem('member');
                                            return member ? JSON.parse(member) : null;
                                          } catch (error) {
                                            return null;
                                          }
                                        })();
                                        if (user) {
                                          setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                        }
                                      }
                                    }}
                                    disabled={announcement?.post_as !== 'member'}
                                    className="sr-only peer"
                                  />
                                  <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-[#3D9D9B] peer-checked:border-4"></div>
                                </div>
                                <span className="ml-2 text-sm text-gray-700">Member</span>
                              </label>
                            </div>
                          )}
                        />
                      </div>
                    </div>
                    {errors.postAs && (
                      <p className="mt-1 text-sm text-red-600">{errors.postAs.message}</p>
                    )}
                  </div>

                  {/* Group Selector - Show when Group is selected */}
                  {watchedValues.postAs === 'Group' && (
                    <div>
                      <GroupSelector
                        value={watch('selectedGroupId')}
                        onChange={handleGroupSelect}
                        error={errors.selectedGroupId?.message}
                        disabled={true} // Disable during editing
                      />
                    </div>
                  )}

                  {/* Member Selector - Show when Member is selected */}
                  {watchedValues.postAs === 'Member' && (
                    <div>
                      <MemberSelector
                        value={watch('selectedMemberId')}
                        onChange={handleMemberSelect}
                        error={errors.selectedMemberId?.message}
                        disabled={true} // Disable during editing
                      />
                    </div>
                  )}

                  {/* Auto Name Field - Only show when Creator is selected */}
                  {watchedValues.postAs === 'Creator' && (
                    <div>
                      <Controller
                        name="autoName"
                        control={control}
                        render={({ field }) => (
                          <input
                            {...field}
                            type="text"
                            disabled
                            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 cursor-not-allowed"
                            placeholder="Auto-filled from Creator Name"
                          />
                        )}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Announcement Information Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-[#3D9D9B] mb-4">Announcement Information</h3>

                {/* Title */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Title <span className="text-[#3D9D9B]">*</span>
                  </label>
                  <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        onChange={(e) => handleTitleChange(e.target.value, field.onChange)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
                        placeholder="Announcement Title (max 10 words)"
                        value={field.value}
                      />
                    )}
                  />
                  {errors.title && (
                    <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
                  )}
                </div>

                {/* Description */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Description
                  </label>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <textarea
                        {...field}
                        rows={4}
                        onChange={(e) => {
                          const words = e.target.value.trim() === ''
                            ? []
                            : e.target.value.trim().split(/\s+/);
                          const limited = words.slice(0, 100).join(' ');
                          field.onChange(limited);
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
                        placeholder="Write your description here... (max 100 words)"
                        value={field.value}
                      />
                    )}
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                  )}
                </div>

                {/* Attachments */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Attachments
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                    <input
                      type="file"
                      multiple
                      accept="image/*,.pdf,.doc,.docx"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="file-upload"
                    />
                    <label
                      htmlFor="file-upload"
                      className="cursor-pointer flex flex-col items-center"
                    >
                      <Upload className="w-8 h-8 text-gray-400 mb-2" />
                      <span className="text-sm text-gray-600">Click to upload files</span>
                    </label>
                  </div>

                  {/* Error Message */}
                  <ErrorMessage message={fileUploadError} />

                  {/* Display uploaded files */}
                  {attachments.length > 0 && (
                    <div className="mt-3 grid grid-cols-3 gap-2">
                      {attachments.map((attachment) => (
                        <div key={attachment.id} className="relative">
                          {attachment.type.startsWith('image/') ? (
                            <img
                              src={attachment.url}
                              alt={attachment.name}
                              className="w-full h-20 object-cover rounded border"
                            />
                          ) : (
                            <div className="w-full h-20 bg-gray-100 rounded border flex items-center justify-center">
                              {attachment.type === 'application/pdf' ? (
                                <div className="flex flex-col items-center">
                                  <svg className="w-8 h-8 text-black font-bold" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                                  </svg>
                                  <span className="text-xs text-black font-bold mt-1">PDF</span>
                                </div>
                              ) : (
                                <div className="flex flex-col items-center">
                                  <svg className="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                                  </svg>
                                  <span className="text-xs text-gray-600 mt-1">DOC</span>
                                </div>
                              )}
                            </div>
                          )}
                          <button
                            type="button"
                            onClick={() => removeAttachment(attachment.id)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Label and Priority Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Label */}
                  <div>
                    <Controller
                      name="label"
                      control={control}
                      render={({ field }) => (
                        <LabelSelector
                          value={field.value}
                          onChange={field.onChange}
                          error={errors.label?.message}
                        />
                      )}
                    />
                  </div>

                  {/* Priority */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Priority <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="priority"
                      control={control}
                      render={({ field }) => (
                        <PriorityDropdown
                          value={field.value}
                          onChange={field.onChange}
                          error={errors.priority?.message}
                        />
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Announcement Visibility Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-[#3D9D9B] mb-4">Announcement Visibility</h3>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {/* Start Date */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Start Date <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="startDate"
                      control={control}
                      render={({ field }) => (
                        <Calendar
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select start date"
                        />
                      )}
                    />
                    {errors.startDate && (
                      <p className="mt-1 text-sm text-red-600">{errors.startDate.message}</p>
                    )}
                  </div>

                  {/* Start Time */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Start Time <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="startTime"
                      control={control}
                      render={({ field }) => (
                        <TimePicker
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select start time"
                        />
                      )}
                    />
                    {errors.startTime && (
                      <p className="mt-1 text-sm text-red-600">{errors.startTime.message}</p>
                    )}
                  </div>

                  {/* End Date */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      End Date <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="endDate"
                      control={control}
                      render={({ field }) => (
                        <Calendar
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select end date"
                        />
                      )}
                    />
                    {errors.endDate && (
                      <p className="mt-1 text-sm text-red-600">{errors.endDate.message}</p>
                    )}
                  </div>

                  {/* End Time */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      End Time <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="endTime"
                      control={control}
                      render={({ field }) => (
                        <TimePicker
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select end time"
                        />
                      )}
                    />
                    {errors.endTime && (
                      <p className="mt-1 text-sm text-red-600">{errors.endTime.message}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Tower and Unit Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Tower */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Tower <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="selectedTowers"
                      control={control}
                      render={({ field }) => (
                        <TowerSelector
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select Towers"
                        />
                      )}
                    />
                    {errors.selectedTowers && (
                      <p className="mt-1 text-sm text-red-600">{errors.selectedTowers.message}</p>
                    )}
                  </div>

                  {/* Unit */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Unit <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="selectedUnits"
                      control={control}
                      render={({ field }) => (
                        <UnitSelector
                          value={field.value}
                          onChange={field.onChange}
                          selectedTowers={selectedTowers}
                          placeholder="Select Units"
                        />
                      )}
                    />
                    {errors.selectedUnits && (
                      <p className="mt-1 text-sm text-red-600">{errors.selectedUnits.message}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-center">
                <button
                  type="submit"
                  disabled={isSubmitting || !isFormValid()}
                  className={`w-full px-8 py-3 rounded-md transition duration-200 font-medium ${
                    isFormValid() && !isSubmitting
                      ? 'bg-[#3D9D9B] text-white hover:bg-[#34877A]'
                      : 'bg-white text-[#3D9D9B] border-2 border-[#3D9D9B] hover:bg-gray-50'
                  } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  {isSubmitting ? 'Updating...' : 'Send'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Success Message Box */}
      <MessageBox
        message={successMessage}
        clearMessage={clearMessage}
        onOk={handleSuccessOk}
      />
    </div>
  );
};

export default EditAnnouncement;
