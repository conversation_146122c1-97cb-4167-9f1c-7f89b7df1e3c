import React, { useState } from 'react';
import { Flag, Image as ImageIcon } from 'lucide-react';
import { VscFilePdf } from "react-icons/vsc";
import { FaFileWord } from "react-icons/fa";
import { HiUserCircle } from "react-icons/hi";
import { FaUserGroup } from "react-icons/fa6";

/**
 * AnnouncementPreview Component
 * Real-time preview of the announcement as it will appear when posted
 */
const AnnouncementPreview = ({ data, currentUser, isInModal = false }) => {
  const [expandedTitle, setExpandedTitle] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(null);

  // Get priority configuration
  const getPriorityConfig = (priority) => {
    const configs = {
      urgent: { color: '#EF4444', bgColor: '#FEF2F2', label: 'Urgent' },
      high: { color: '#F59E0B', bgColor: '#FFFBEB', label: 'High' },
      normal: { color: '#3D9D9B', bgColor: '#F0FDF4', label: 'Normal' },
      low: { color: '#6B7280', bgColor: '#F9FAFB', label: 'Low' }
    };
    return configs[priority?.toLowerCase()] || null;
  };

  // Get announcement status
  const getStatus = () => {
    if (!data.startDate || !data.startTime || !data.endDate || !data.endTime) {
      return { status: 'Draft', color: '#6B7280', bgColor: '#F9FAFB' };
    }

    try {
      const now = new Date();

      // Handle different date formats and ensure proper parsing
      let startDateStr = data.startDate;
      let endDateStr = data.endDate;

      // If dates are in YYYY-MM-DD format, use them directly
      if (typeof data.startDate === 'string' && data.startDate.includes('-')) {
        startDateStr = data.startDate;
      } else {
        // Convert to YYYY-MM-DD format if needed
        const startDateObj = new Date(data.startDate);
        startDateStr = startDateObj.toISOString().split('T')[0];
      }

      if (typeof data.endDate === 'string' && data.endDate.includes('-')) {
        endDateStr = data.endDate;
      } else {
        // Convert to YYYY-MM-DD format if needed
        const endDateObj = new Date(data.endDate);
        endDateStr = endDateObj.toISOString().split('T')[0];
      }

      // Create datetime objects for comparison
      const startDateTime = new Date(`${startDateStr}T${data.startTime}`);
      const endDateTime = new Date(`${endDateStr}T${data.endTime}`);

      if (now < startDateTime) {
        return { status: 'Upcoming', color: '#F59E0B', bgColor: '#FFFBEB' };
      } else if (now >= startDateTime && now <= endDateTime) {
        return { status: 'On Going', color: '#10B981', bgColor: '#F0FDF4' };
      } else {
        return { status: 'Expired', color: '#EF4444', bgColor: '#FEF2F2' };
      }
    } catch (error) {
      console.warn('Error calculating status in preview:', error);
      return { status: 'Draft', color: '#6B7280', bgColor: '#F9FAFB' };
    }
  };

  // Format date and time
  const formatDateTime = (date, time) => {
    if (!date || !time) return '';

    let formattedDate = '';

    // Handle different date formats
    if (typeof date === 'string') {
      // If date is in YYYY-MM-DD format (from Calendar component)
      if (date.includes('-') && date.length === 10) {
        const [year, month, day] = date.split('-');
        formattedDate = `${day}-${month}-${year}`;
      } else {
        // If date is already formatted or other string format
        const dateObj = new Date(date);
        const day = dateObj.getDate().toString().padStart(2, '0');
        const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
        const year = dateObj.getFullYear();
        formattedDate = `${day}-${month}-${year}`;
      }
    } else if (date instanceof Date) {
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      formattedDate = `${day}-${month}-${year}`;
    } else {
      // Fallback for other formats
      const dateObj = new Date(date);
      const day = dateObj.getDate().toString().padStart(2, '0');
      const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
      const year = dateObj.getFullYear();
      formattedDate = `${day}-${month}-${year}`;
    }

    // Format time (convert 24-hour to 12-hour if needed)
    let formattedTime = time;
    if (time.includes(':') && !time.includes('AM') && !time.includes('PM')) {
      // Convert 24-hour format to 12-hour format
      const [hours, minutes] = time.split(':');
      const hour24 = parseInt(hours);
      const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
      const period = hour24 >= 12 ? 'pm' : 'am';
      formattedTime = `${hour12}:${minutes}${period}`;
    }

    return `${formattedDate} at ${formattedTime}`;
  };

  // Handle title expansion
  const handleTitleHover = () => {
    if (data.title && data.title.length > 25) {
      setExpandedTitle(true);
    }
  };

  const handleTitleLeave = () => {
    setExpandedTitle(false);
  };

  // Handle title click for mobile/touch devices
  const handleTitleClick = () => {
    if (data.title && data.title.length > 25) {
      setExpandedTitle(!expandedTitle);
    }
  };

  // Handle image click
  const handleImageClick = (index) => {
    // Only open modal for actual images, not PDFs or DOCs
    const file = data.attachments[index];
    if (file && isImage(file.name, file.type)) {
      setSelectedImageIndex(index);
    }
  };

  // Close image modal
  const closeImageModal = () => {
    setSelectedImageIndex(null);
  };

  // Helper function to check if file is a PDF
  const isPDF = (fileName, fileType) => {
    if (fileType) {
      return fileType === 'application/pdf';
    }
    return fileName?.toLowerCase().endsWith('.pdf');
  };

  // Helper function to check if file is a DOC
  const isDoc = (fileName, fileType) => {
    if (fileType) {
      return fileType === 'application/msword' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    }
    return fileName?.toLowerCase().match(/\.(doc|docx)$/);
  };

  // Helper function to check if file is an image
  const isImage = (fileName, fileType) => {
    if (fileType) {
      return fileType.startsWith('image/');
    }
    return fileName?.toLowerCase().match(/\.(jpg|jpeg|png|gif)$/);
  };

  const priorityConfig = getPriorityConfig(data.priority);
  const statusConfig = getStatus();

  return (
    <div className="space-y-4 ">
      {/* Preview Header */}
      <div className="text-center pb-4 border-b border-gray-200">
        {/* <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">
          Live Preview
        </h4>
        <p className="text-xs text-gray-400 mt-1">
          This is how your announcement will appear
        </p> */}
      </div>

      {/* Announcement Card Preview */}
      <div className="border border-[#3D9D9B] rounded-lg overflow-hidden bg-white shadow-sm ">
        {/* Header with Author Info and Status */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-start justify-between">
            {/* Left side - Author Info */}
            <div className="flex items-center space-x-3">
              {/* Author Avatar and Info */}
              <div className="w-8 h-8 rounded-full flex items-center justify-center">
                {(data.postAs === 'Creator' || data.postAs === 'creator') ? (
                 <HiUserCircle className="w-8 h-8" color="gray" />

                ) : (
                  <FaUserGroup className="w-8 h-8" color="gray" />
                )}
              </div>
              <div>
                <h3 className="text-sm font-bold text-gray-900">
                  {data.postAs === 'Group'
                    ? data.selectedGroupName || 'Group Name'
                    : data.postAs === 'Member'
                      ? (data.selectedMemberName || 'Member Name')
                      : (data.authorName || 'Author Name')
                  }
                </h3>
                <p className="text-xs font-bold text-gray-900">
                  Creator: {data.authorName || currentUser?.fullName || currentUser?.full_name || 'Not specified'}
                </p>
              </div>
            </div>

            {/* Right side - Priority Flag, Status and Notification */}
            <div className="flex items-center space-x-2">
              {/* Priority Flag */}
              {data.priority && priorityConfig && (
                <Flag
                  className="w-4 h-4"
                  style={{ color: priorityConfig.color }}
                  fill={priorityConfig.color}
                />
              )}
              <div
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: statusConfig.color }}
              />
              {/* <span className="text-xs text-gray-500">🔔</span> */}
              <span className="text-xs font-medium text-gray-700">30</span>
            </div>
          </div>

          {/* Date Info */}
          <div className="mt-2 text-xs">
            <span style={{ color: '#3D9D9B' }}>Start: {formatDateTime(data.startDate, data.startTime) || ''}</span>
            <span className="ml-4" style={{ color: '#FF8682' }}>Expire: {formatDateTime(data.endDate, data.endTime) || ''}</span>
          </div>

          {/* Label Info - Next line */}
          {data.label && (
            <div className="mt-1 text-xs">
              <div className="flex flex-wrap gap-1">
                {data.label.split(',').map((label, index) => (
                  <span
                    key={index}
                    className="bg-[#3D9D9B] text-white text-[10px] px-2 py-1 rounded font-medium"
                  >
                    {label.trim()}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Title */}
          <div
            className="mb-3 cursor-pointer"
            onMouseEnter={handleTitleHover}
            onMouseLeave={handleTitleLeave}
            onClick={handleTitleClick}
          >
            <h4
              className="text-sm font-bold text-gray-900 leading-relaxed break-words transition-all duration-200"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: expandedTitle ? 'unset' : 2,
                WebkitBoxOrient: 'vertical',
                overflow: expandedTitle ? 'visible' : 'hidden',
                textOverflow: expandedTitle ? 'unset' : 'ellipsis'
              }}
            >
              {data.title || 'Announcements can be about new policies.'}
            </h4>
          </div>

          {/* Description */}
          <div className="mb-4">
            <p className="text-gray-500 text-xs leading-relaxed whitespace-pre-wrap break-words font-bold">
              {data.description || 'Description goes here.'}
            </p>
          </div>

          {/* Tower and Unit Selection Display */}
          {(data.selectedTowers && data.selectedTowers.length > 0) || (data.selectedUnits && data.selectedUnits.length > 0) ? (
            <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
              <h5 className="text-xs font-semibold text-gray-700 mb-2">Audience:</h5>

              {/* Selected Towers */}
              {data.selectedTowers && data.selectedTowers.length > 0 && (
                <div className="mb-2">
                  <span className="text-xs text-gray-600 font-medium">Towers: </span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {data.selectedTowers.map((tower, index) => (
                      <span key={index} className="inline-block bg-[#3D9D9B] text-white text-[10px] px-2 py-1 rounded">
                        {tower}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Selected Units */}
              {data.selectedUnits && data.selectedUnits.length > 0 && (
                <div>
                  <span className="text-xs text-gray-600 font-medium">Units: </span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {data.selectedUnits.slice(0, 10).map((unit, index) => (
                      <span key={index} className="inline-block bg-gray-200 text-gray-700 text-[10px] px-2 py-1 rounded">
                        {unit}
                      </span>
                    ))}
                    {data.selectedUnits.length > 10 && (
                      <span className="inline-block bg-gray-300 text-gray-600 text-[10px] px-2 py-1 rounded">
                        +{data.selectedUnits.length - 10} more
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : null}

          {/* Attachments */}
          <div className="mb-4">
            {data.attachments && data.attachments.length > 0 ? (
              <div className="grid grid-cols-2 gap-2">
                {data.attachments.slice(0, 4).map((file, index) => (
                  <div
                    key={index}
                    className={`relative aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-all duration-200 ${
                      isInModal
                        ? 'border-4 border-[#3D9D9B] shadow-xl hover:shadow-2xl hover:border-[#2A7A78] transform hover:scale-105'
                        : ''
                    }`}
                    onClick={() => handleImageClick(index)}
                  >
                    {isPDF(file.name, file.type) ? (
                      <div className="w-full h-full flex items-center justify-center">
                        <VscFilePdf className="w-12 h-12 text-black font-bold" />
                      </div>
                    ) : isDoc(file.name, file.type) ? (
                      <div className="w-full h-full flex items-center justify-center">
                        <FaFileWord className="w-12 h-12 text-blue-500" />
                      </div>
                    ) : file.preview ? (
                      <div className={`w-full h-full ${isInModal ? 'p-1' : ''}`}>
                        <img
                          src={file.preview}
                          alt={file.name}
                          className={`w-full h-full object-cover ${
                            isInModal ? 'rounded-sm border border-gray-200' : ''
                          }`}
                        />
                      </div>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <ImageIcon className="w-8 h-8 text-gray-400" />
                      </div>
                    )}
                    {index === 3 && data.attachments.length > 4 && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <span className="text-white font-medium">
                          +{data.attachments.length - 3}
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              /* Default preview image placeholder */
              <div className="w-full text-white p-4 rounded text-center">
                <div className="text-lg font-bold mb-2">No Attachments</div>
                <div className="text-xs leading-relaxed">
                 Click to add attachments
                </div>
                <div className="mt-2 text-xs">
                
                </div>
              </div>
            )}
          </div>


        </div>

      </div>

      {/* Image Modal */}
      {selectedImageIndex !== null && data.attachments && data.attachments[selectedImageIndex] &&
       isImage(data.attachments[selectedImageIndex].name, data.attachments[selectedImageIndex].type) && (
        <div
          className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-4"
          onClick={closeImageModal}
        >
          <div className="relative max-w-4xl max-h-full">
            <img
              src={data.attachments[selectedImageIndex].preview}
              alt={data.attachments[selectedImageIndex].name}
              className="max-w-full max-h-full object-contain"
            />
            <button
              onClick={closeImageModal}
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-opacity"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnnouncementPreview;
