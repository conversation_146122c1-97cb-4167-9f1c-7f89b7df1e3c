import React from 'react';
import { FaThumbtack } from 'react-icons/fa';
import { announcementApi } from '../../../../api/announcementsApi/announcementsBackendApi';

/**
 * usePinPost Hook
 * Handles pin/unpin functionality for announcements
 * Provides pin-related utilities and functions
 */
const usePinPost = ({
    announcements,
    setAnnouncements,
    onPinSuccess,
    onPinError
}) => {
    
    /**
     * Handle pin/unpin post functionality
     * @param {string} announcementId - The ID of the announcement to pin/unpin
     */
    const handlePinPost = async (announcementId) => {
        try {
            // Call API to toggle pin status
            const response = await announcementApi.togglePin(announcementId);

            // Update local state based on API response
            setAnnouncements(prevAnnouncements => {
                const updatedAnnouncements = prevAnnouncements.map(announcement => {
                    if (announcement.id === announcementId) {
                        const newPinnedState = response.is_pinned;

                        // Show feedback message
                        if (newPinnedState) {
                            console.log('📌 Announcement pinned! It will appear first among pinned posts.');
                            if (onPinSuccess) {
                                onPinSuccess('Announcement pinned successfully! It will appear first among pinned posts.');
                            }
                        } else {
                            console.log('📌 Announcement unpinned! It will return to normal position.');
                            if (onPinSuccess) {
                                onPinSuccess('Announcement unpinned successfully! It will return to normal position.');
                            }
                        }

                        return {
                            ...announcement,
                            pinned: newPinnedState,
                            isPinned: newPinnedState
                        };
                    }
                    return announcement;
                });

                return updatedAnnouncements;
            });
        } catch (error) {
            console.error('Error pinning/unpinning announcement:', error);
            if (onPinError) {
                onPinError('Failed to pin/unpin announcement. Please try again.');
            }

            // Fallback to local update if API fails
            setAnnouncements(prevAnnouncements => {
                const updatedAnnouncements = prevAnnouncements.map(announcement => {
                    if (announcement.id === announcementId) {
                        const newPinnedState = !announcement.pinned;
                        return {
                            ...announcement,
                            pinned: newPinnedState,
                            isPinned: newPinnedState
                        };
                    }
                    return announcement;
                });
                return updatedAnnouncements;
            });
        }
    };

    /**
     * Sort announcements with pinned posts first (most recently pinned first)
     * @param {Array} announcementList - List of announcements to sort
     * @returns {Array} - Sorted announcements array
     */
    const sortAnnouncementsWithPinned = (announcementList) => {
        return announcementList.sort((a, b) => {
            // Pinned announcements come first
            if (a.pinned && !b.pinned) return -1;
            if (!a.pinned && b.pinned) return 1;

            // If both are pinned, sort by most recent pin time first
            // If pinnedAt is not available, fall back to creation date
            if (a.pinned && b.pinned) {
                const aPinnedTime = new Date(a.pinnedAt || a.createdAt || a.startDate);
                const bPinnedTime = new Date(b.pinnedAt || b.createdAt || b.startDate);
                return bPinnedTime - aPinnedTime; // Most recently pinned first
            }

            // If both are not pinned, sort by most recent creation date first
            if (!a.pinned && !b.pinned) {
                const aDateTime = new Date(a.createdAt || `${a.startDate} ${a.startTime}`);
                const bDateTime = new Date(b.createdAt || `${b.startDate} ${b.startTime}`);
                return bDateTime - aDateTime; // Most recent first
            }

            return 0;
        });
    };

    /**
     * Get pinned announcements count
     * @returns {number} - Number of pinned announcements
     */
    const getPinnedCount = () => {
        return announcements.filter(announcement => announcement.pinned).length;
    };

    /**
     * Get the most recently pinned announcement
     * @returns {Object|null} - Most recently pinned announcement or null
     */
    const getMostRecentlyPinned = () => {
        const pinnedAnnouncements = announcements.filter(announcement => announcement.pinned);
        if (pinnedAnnouncements.length === 0) return null;

        return pinnedAnnouncements.reduce((mostRecent, current) => {
            const currentPinnedTime = new Date(current.pinnedAt || current.createdAt || current.startDate);
            const mostRecentPinnedTime = new Date(mostRecent.pinnedAt || mostRecent.createdAt || mostRecent.startDate);
            return currentPinnedTime > mostRecentPinnedTime ? current : mostRecent;
        });
    };

    /**
     * Get all pinned announcements sorted by pin time (most recent first)
     * @returns {Array} - Sorted pinned announcements
     */
    const getPinnedAnnouncementsSorted = () => {
        const pinnedAnnouncements = announcements.filter(announcement => announcement.pinned);
        return pinnedAnnouncements.sort((a, b) => {
            const aPinnedTime = new Date(a.pinnedAt || a.createdAt || a.startDate);
            const bPinnedTime = new Date(b.pinnedAt || b.createdAt || b.startDate);
            return bPinnedTime - aPinnedTime; // Most recently pinned first
        });
    };

    /**
     * Check if announcement is pinned
     * @param {string} announcementId - The ID of the announcement
     * @returns {boolean} - True if announcement is pinned
     */
    const isAnnouncementPinned = (announcementId) => {
        const announcement = announcements.find(ann => ann.id === announcementId);
        return announcement ? announcement.pinned : false;
    };

    /**
     * Render pin icon for announcement card
     * @param {Object} announcement - The announcement object
     * @returns {JSX.Element|null} - Pin icon component or null
     */
    const renderPinIcon = (announcement) => {
        if (!announcement.pinned) return null;

        return (
            <FaThumbtack 
                className="w-[16px] h-[16px] text-[#3D9D9B] transform rotate-45" 
                title="Pinned Post" 
            />
        );
    };

    /**
     * Get pin button text based on current state
     * @param {Object} announcement - The announcement object
     * @returns {string} - Button text
     */
    const getPinButtonText = (announcement) => {
        return announcement.pinned ? 'Unpin Post' : 'Pin Post';
    };

    /**
     * Get pin button icon class based on current state
     * @param {Object} announcement - The announcement object
     * @returns {string} - Icon class
     */
    const getPinButtonIconClass = (announcement) => {
        return announcement.pinned 
            ? 'text-[#3D9D9B] hover:text-[#2A7A78]' 
            : 'text-black-600 hover:text-black-800';
    };

    // Return the functions and utilities that can be used by parent components
    return {
        handlePinPost,
        sortAnnouncementsWithPinned,
        getPinnedCount,
        getMostRecentlyPinned,
        getPinnedAnnouncementsSorted,
        isAnnouncementPinned,
        renderPinIcon,
        getPinButtonText,
        getPinButtonIconClass
    };
};

/**
 * PinIcon Component
 * Renders the pin icon for pinned announcements
 */
export const PinIcon = ({ announcement }) => {
    // Check both pinned and isPinned properties for compatibility
    const isPinned = announcement.pinned || announcement.isPinned;
    if (!isPinned) return null;

    return (
        <FaThumbtack
            className="w-[16px] h-[16px] text-[#3D9D9B] transform rotate-45"
            title="Pinned Post"
        />
    );
};

export default usePinPost;
